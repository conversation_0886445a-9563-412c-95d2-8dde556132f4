#!/usr/bin/env python3
"""
Test script for Azure OpenAI provider functionality.
Tests provider registration, configuration, and basic instantiation.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.llm.base import LLMProviderFactory, ChatMessage
from app.llm.azure_openai_provider import AzureOpenAIProvider, create_azure_openai_provider
from app.core.exceptions import LL<PERSON>rror


def test_azure_openai_provider_registration():
    """Test that Azure OpenAI provider is properly registered."""
    print("🔍 Testing Azure OpenAI provider registration...")
    
    # Check if azure_openai is in the list of available providers
    available_providers = LLMProviderFactory.list_providers()
    
    if "azure_openai" in available_providers:
        print("   ✅ Azure OpenAI provider is registered")
        return True
    else:
        print(f"   ❌ Azure OpenAI provider not found in: {available_providers}")
        return False


def test_azure_openai_provider_instantiation():
    """Test Azure OpenAI provider instantiation with mock config."""
    print("🔍 Testing Azure OpenAI provider instantiation...")
    
    # Mock configuration for testing
    test_config = {
        "api_key": "LGxHqNYJ0rCMCYjIwgGh4aFIanja9WIbFCnP36ZF2BZJkdAxJS5HJQQJ99BFAC4f1cMXJ3w3AAABACOGIW6f",
        "endpoint": "https://xapa-open-ai.openai.azure.com/",
        "api_version": "2025-01-01-preview",
        "deployment": "gpt-4.1",
        "temperature": 0.7,
        "max_tokens": 1000
    }
    
    try:
        # Test direct instantiation
        provider = AzureOpenAIProvider(test_config)
        
        # Check basic properties
        if provider.deployment == "gpt-4.1":
            print("   ✅ Provider instantiated with correct deployment")
        else:
            print(f"   ❌ Incorrect deployment: {provider.deployment}")
            return False
            
        if provider.default_temperature == 0.7:
            print("   ✅ Provider instantiated with correct temperature")
        else:
            print(f"   ❌ Incorrect temperature: {provider.default_temperature}")
            return False
            
        # Test model info
        model_info = provider.get_model_info()
        if model_info["provider"] == "AzureOpenAIProvider":
            print("   ✅ Provider returns correct model info")
        else:
            print(f"   ❌ Incorrect provider name in model info: {model_info}")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ Provider instantiation failed: {str(e)}")
        return False


def test_azure_openai_provider_factory():
    """Test Azure OpenAI provider creation through factory."""
    print("🔍 Testing Azure OpenAI provider factory...")
    
    # Mock configuration
    test_config = {
        "api_key": "LGxHqNYJ0rCMCYjIwgGh4aFIanja9WIbFCnP36ZF2BZJkdAxJS5HJQQJ99BFAC4f1cMXJ3w3AAABACOGIW6f",
        "endpoint": "https://xapa-open-ai.openai.azure.com/",
        "api_version": "2025-01-01-preview",
        "deployment": "gpt-4.1"
    }
    try:
        # Test factory creation
        provider = LLMProviderFactory.create_provider("azure_openai", test_config)
        
        if isinstance(provider, AzureOpenAIProvider):
            print("   ✅ Factory created Azure OpenAI provider correctly")
            return True
        else:
            print(f"   ❌ Factory created wrong type: {type(provider)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Factory creation failed: {str(e)}")
        return False


def test_azure_openai_convenience_function():
    """Test Azure OpenAI convenience creation function."""
    print("🔍 Testing Azure OpenAI convenience function...")
    
    # Mock configuration
    test_config = {
        "api_key": "LGxHqNYJ0rCMCYjIwgGh4aFIanja9WIbFCnP36ZF2BZJkdAxJS5HJQQJ99BFAC4f1cMXJ3w3AAABACOGIW6f",
        "endpoint": "https://xapa-open-ai.openai.azure.com/",
        "api_version": "2025-01-01-preview",
        "deployment": "gpt-4.1"
    }
    
    try:
        # Test convenience function
        provider = create_azure_openai_provider(test_config)
        
        if isinstance(provider, AzureOpenAIProvider):
            print("   ✅ Convenience function created provider correctly")
            return True
        else:
            print(f"   ❌ Convenience function created wrong type: {type(provider)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Convenience function failed: {str(e)}")
        return False


def test_azure_openai_missing_config():
    """Test Azure OpenAI provider with missing required configuration."""
    print("🔍 Testing Azure OpenAI provider with missing config...")
    
    # Test missing API key
    try:
        config_no_key = {
            "endpoint": "https://xapa-open-ai.openai.azure.com/",
            "deployment": "gpt-4.1"
        }
        provider = AzureOpenAIProvider(config_no_key)
        print("   ❌ Should have failed with missing API key")
        return False
    except LLMError as e:
        if "API key is required" in str(e):
            print("   ✅ Correctly failed with missing API key")
        else:
            print(f"   ❌ Wrong error for missing API key: {str(e)}")
            return False
    except Exception as e:
        print(f"   ❌ Unexpected error for missing API key: {str(e)}")
        return False
    
    # Test missing endpoint
    try:
        config_no_endpoint = {
            "api_key": "test_key",
            "deployment": "gpt-4.1"
        }
        provider = AzureOpenAIProvider(config_no_endpoint)
        print("   ❌ Should have failed with missing endpoint")
        return False
    except LLMError as e:
        if "endpoint is required" in str(e):
            print("   ✅ Correctly failed with missing endpoint")
        else:
            print(f"   ❌ Wrong error for missing endpoint: {str(e)}")
            return False
    except Exception as e:
        print(f"   ❌ Unexpected error for missing endpoint: {str(e)}")
        return False
    
    # Test missing deployment
    try:
        config_no_deployment = {
            "api_key": "test_key",
            "endpoint": "https://xapa-open-ai.openai.azure.com/"
        }
        provider = AzureOpenAIProvider(config_no_deployment)
        print("   ❌ Should have failed with missing deployment")
        return False
    except LLMError as e:
        if "deployment name is required" in str(e):
            print("   ✅ Correctly failed with missing deployment")
            return True
        else:
            print(f"   ❌ Wrong error for missing deployment: {str(e)}")
            return False
    except Exception as e:
        print(f"   ❌ Unexpected error for missing deployment: {str(e)}")
        return False


def test_message_conversion():
    """Test message format conversion."""
    print("🔍 Testing message format conversion...")
    
    test_config = {
        "api_key": "LGxHqNYJ0rCMCYjIwgGh4aFIanja9WIbFCnP36ZF2BZJkdAxJS5HJQQJ99BFAC4f1cMXJ3w3AAABACOGIW6f",
        "endpoint": "https://xapa-open-ai.openai.azure.com/",
        "deployment": "gpt-4.1"
    }
    
    try:
        provider = AzureOpenAIProvider(test_config)
        
        # Test message conversion
        messages = [
            ChatMessage(role="system", content="You are a helpful assistant."),
            ChatMessage(role="user", content="Hello, how are you?"),
            ChatMessage(role="assistant", content="I'm doing well, thank you!")
        ]
        
        converted = provider._convert_messages(messages)
        
        if len(converted) == 3:
            print("   ✅ Correct number of messages converted")
        else:
            print(f"   ❌ Wrong number of messages: {len(converted)}")
            return False
            
        if converted[0]["role"] == "system" and converted[0]["content"] == "You are a helpful assistant.":
            print("   ✅ System message converted correctly")
        else:
            print(f"   ❌ System message conversion failed: {converted[0]}")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ Message conversion failed: {str(e)}")
        return False


def run_all_tests():
    """Run all Azure OpenAI provider tests."""
    print("🧪 Starting Azure OpenAI Provider Tests\n")
    
    tests = [
        ("Provider Registration", test_azure_openai_provider_registration),
        ("Provider Instantiation", test_azure_openai_provider_instantiation),
        ("Provider Factory", test_azure_openai_provider_factory),
        ("Convenience Function", test_azure_openai_convenience_function),
        ("Missing Config Validation", test_azure_openai_missing_config),
        ("Message Conversion", test_message_conversion),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print(f"\n🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All Azure OpenAI provider tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    if not success:
        sys.exit(1)
