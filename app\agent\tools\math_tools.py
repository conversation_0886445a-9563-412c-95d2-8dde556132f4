import re
import math
from typing import Dict, Any
from app.agent.tools.base import <PERSON>Tool, ToolSchema, ToolResult, tool_registry


class MathSolverTool(BaseTool):
    """Tool for solving basic mathematical expressions and equations."""
    
    @property
    def name(self) -> str:
        return "math_solver"
    
    @property
    def description(self) -> str:
        return "Solve mathematical expressions and basic equations. Supports arithmetic, algebra, and common mathematical functions."
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "Mathematical expression or equation to solve"
                    }
                }
            },
            required=["expression"]
        )
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Execute mathematical calculation.
        
        Args:
            expression: Mathematical expression to evaluate
            
        Returns:
            ToolResult: Calculation result
        """
        try:
            expression = kwargs.get("expression", "").strip()
            
            if not expression:
                return ToolResult(
                    success=False,
                    error="No expression provided"
                )
            
            # Basic safety check - only allow safe mathematical operations
            if not self._is_safe_expression(expression):
                return ToolResult(
                    success=False,
                    error="Expression contains unsafe operations"
                )
            
            # Replace common mathematical functions
            safe_expression = self._prepare_expression(expression)
            
            try:
                # Evaluate the expression
                result = eval(safe_expression, {"__builtins__": {}}, {
                    "sin": math.sin,
                    "cos": math.cos,
                    "tan": math.tan,
                    "sqrt": math.sqrt,
                    "log": math.log,
                    "log10": math.log10,
                    "exp": math.exp,
                    "pi": math.pi,
                    "e": math.e,
                    "abs": abs,
                    "pow": pow,
                    "round": round
                })
                
                return ToolResult(
                    success=True,
                    result=result,
                    metadata={
                        "original_expression": expression,
                        "evaluated_expression": safe_expression
                    }
                )
                
            except ZeroDivisionError:
                return ToolResult(
                    success=False,
                    error="Division by zero"
                )
            except ValueError as e:
                return ToolResult(
                    success=False,
                    error=f"Mathematical error: {str(e)}"
                )
            except Exception as e:
                return ToolResult(
                    success=False,
                    error=f"Calculation error: {str(e)}"
                )
                
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Tool execution error: {str(e)}"
            )
    
    def _is_safe_expression(self, expression: str) -> bool:
        """Check if expression contains only safe mathematical operations."""
        # Whitelist of allowed characters and functions
        allowed_pattern = r'^[0-9+\-*/().\s\w]*$'
        
        if not re.match(allowed_pattern, expression):
            return False
        
        # Blacklist dangerous operations
        dangerous = ['import', 'exec', 'eval', '__', 'open', 'file', 'input', 'raw_input']
        for danger in dangerous:
            if danger in expression.lower():
                return False
        
        return True
    
    def _prepare_expression(self, expression: str) -> str:
        """Prepare expression for safe evaluation."""
        # Replace common mathematical notation
        expression = expression.replace('^', '**')  # Power notation
        expression = re.sub(r'(\d)([a-zA-Z])', r'\1*\2', expression)  # Implicit multiplication
        
        return expression


class GeneralCalculatorTool(BaseTool):
    """Tool for general calculations and number operations."""
    
    @property
    def name(self) -> str:
        return "general_calculator"
    
    @property
    def description(self) -> str:
        return "Perform general calculations, unit conversions, and number operations."
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "description": "Type of calculation (calculate, percentage, convert_units)"
                    },
                    "value": {
                        "type": "number",
                        "description": "Input value for calculation"
                    },
                    "expression": {
                        "type": "string",
                        "description": "Mathematical expression for calculation"
                    },
                    "from_unit": {
                        "type": "string",
                        "description": "Source unit for conversion"
                    },
                    "to_unit": {
                        "type": "string",
                        "description": "Target unit for conversion"
                    }
                }
            },
            required=["operation"]
        )
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute general calculation."""
        try:
            operation = kwargs.get("operation", "").lower()
            
            if operation == "percentage":
                return await self._calculate_percentage(kwargs)
            elif operation == "convert_units":
                return await self._convert_units(kwargs)
            elif operation == "calculate":
                return await self._general_calculate(kwargs)
            else:
                return ToolResult(
                    success=False,
                    error=f"Unknown operation: {operation}"
                )
                
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Calculator error: {str(e)}"
            )
    
    async def _calculate_percentage(self, kwargs) -> ToolResult:
        """Calculate percentage operations."""
        value = kwargs.get("value")
        percentage = kwargs.get("percentage", 0)
        
        if value is None:
            return ToolResult(success=False, error="Value required for percentage calculation")
        
        result = value * (percentage / 100)
        return ToolResult(
            success=True,
            result=result,
            metadata={"operation": "percentage", "value": value, "percentage": percentage}
        )
    
    async def _convert_units(self, kwargs) -> ToolResult:
        """Convert between units."""
        # Basic unit conversions
        conversions = {
            ("celsius", "fahrenheit"): lambda c: (c * 9/5) + 32,
            ("fahrenheit", "celsius"): lambda f: (f - 32) * 5/9,
            ("meters", "feet"): lambda m: m * 3.28084,
            ("feet", "meters"): lambda f: f / 3.28084,
            ("kilometers", "miles"): lambda k: k * 0.621371,
            ("miles", "kilometers"): lambda m: m / 0.621371,
        }
        
        value = kwargs.get("value")
        from_unit = kwargs.get("from_unit", "").lower()
        to_unit = kwargs.get("to_unit", "").lower()
        
        if value is None:
            return ToolResult(success=False, error="Value required for unit conversion")
        
        conversion_key = (from_unit, to_unit)
        if conversion_key not in conversions:
            return ToolResult(
                success=False,
                error=f"Conversion from {from_unit} to {to_unit} not supported"
            )
        
        result = conversions[conversion_key](value)
        return ToolResult(
            success=True,
            result=result,
            metadata={
                "operation": "unit_conversion",
                "value": value,
                "from_unit": from_unit,
                "to_unit": to_unit
            }
        )
    
    async def _general_calculate(self, kwargs) -> ToolResult:
        """Perform general calculation."""
        expression = kwargs.get("expression", "")
        
        # Use the math solver for general calculations
        math_solver = MathSolverTool()
        return await math_solver.execute(expression=expression)


# Register tools
tool_registry.register_tool(MathSolverTool())
tool_registry.register_tool(GeneralCalculatorTool())