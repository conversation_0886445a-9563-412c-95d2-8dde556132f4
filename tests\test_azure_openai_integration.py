#!/usr/bin/env python3
"""
Integration test for Azure OpenAI provider with the main provider factory.
Tests that Azure OpenAI can be used as a drop-in replacement for other providers.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.llm.provider_factory import create_llm_provider, get_default_provider_config
from app.llm.azure_openai_provider import AzureOpenAIProvider
from app.core.exceptions import LLMError


def test_azure_openai_in_provider_factory():
    """Test Azure OpenAI provider through the main provider factory."""
    print("🔍 Testing Azure OpenAI through provider factory...")
    
    # Mock configuration
    test_config = {
        "api_key": "test_api_key",
        "endpoint": "https://test-resource.openai.azure.com/",
        "api_version": "2024-02-15-preview",
        "deployment": "test-deployment"
    }
    
    try:
        # Test creation through main factory
        provider = create_llm_provider("azure_openai", test_config)
        
        if isinstance(provider, AzureOpenAIProvider):
            print("   ✅ Main factory created Azure OpenAI provider correctly")
            return True
        else:
            print(f"   ❌ Main factory created wrong type: {type(provider)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Main factory creation failed: {str(e)}")
        return False


def test_azure_openai_default_config():
    """Test Azure OpenAI default configuration retrieval."""
    print("🔍 Testing Azure OpenAI default configuration...")
    
    try:
        # Test getting default config
        default_config = get_default_provider_config("azure_openai")
        
        expected_keys = ["deployment", "api_key", "endpoint", "api_version", "temperature", "max_tokens"]
        
        for key in expected_keys:
            if key not in default_config:
                print(f"   ❌ Missing key in default config: {key}")
                return False
        
        if default_config["temperature"] == 0.7:
            print("   ✅ Default temperature is correct")
        else:
            print(f"   ❌ Wrong default temperature: {default_config['temperature']}")
            return False
            
        if default_config["max_tokens"] == 2000:
            print("   ✅ Default max_tokens is correct")
        else:
            print(f"   ❌ Wrong default max_tokens: {default_config['max_tokens']}")
            return False
            
        print("   ✅ Default configuration is correct")
        return True
        
    except Exception as e:
        print(f"   ❌ Default config retrieval failed: {str(e)}")
        return False


def test_azure_openai_config_override():
    """Test Azure OpenAI configuration override behavior."""
    print("🔍 Testing Azure OpenAI configuration override...")
    
    # Base configuration
    base_config = {
        "api_key": "base_key",
        "endpoint": "https://base-resource.openai.azure.com/",
        "deployment": "base-deployment"
    }
    
    # Override configuration
    override_config = {
        "temperature": 0.9,
        "max_tokens": 1500,
        "api_key": "override_key"
    }
    
    try:
        # Test creation with override
        provider = create_llm_provider("azure_openai", {**base_config, **override_config})
        
        if provider.default_temperature == 0.9:
            print("   ✅ Temperature override worked")
        else:
            print(f"   ❌ Temperature override failed: {provider.default_temperature}")
            return False
            
        if provider.default_max_tokens == 1500:
            print("   ✅ Max tokens override worked")
        else:
            print(f"   ❌ Max tokens override failed: {provider.default_max_tokens}")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration override test failed: {str(e)}")
        return False


def test_azure_openai_provider_comparison():
    """Test that Azure OpenAI provider behaves consistently with other providers."""
    print("🔍 Testing Azure OpenAI provider consistency...")
    
    # Test configurations for different providers
    azure_config = {
        "api_key": "test_key",
        "endpoint": "https://test-resource.openai.azure.com/",
        "deployment": "test-deployment"
    }
    
    openai_config = {
        "api_key": "test_key",
        "model": "gpt-4"
    }
    
    try:
        # Create both providers
        azure_provider = create_llm_provider("azure_openai", azure_config)
        openai_provider = create_llm_provider("openai", openai_config)
        
        # Test that both have the same interface
        azure_info = azure_provider.get_model_info()
        openai_info = openai_provider.get_model_info()
        
        # Both should have provider and config keys
        if "provider" in azure_info and "config" in azure_info:
            print("   ✅ Azure OpenAI provider has correct model info structure")
        else:
            print(f"   ❌ Azure OpenAI provider missing model info keys: {azure_info}")
            return False
            
        if "provider" in openai_info and "config" in openai_info:
            print("   ✅ OpenAI provider has correct model info structure")
        else:
            print(f"   ❌ OpenAI provider missing model info keys: {openai_info}")
            return False
            
        # Both should have the same default temperature and max_tokens
        if azure_provider.default_temperature == openai_provider.default_temperature:
            print("   ✅ Both providers have same default temperature")
        else:
            print(f"   ❌ Temperature mismatch: Azure={azure_provider.default_temperature}, OpenAI={openai_provider.default_temperature}")
            return False
            
        if azure_provider.default_max_tokens == openai_provider.default_max_tokens:
            print("   ✅ Both providers have same default max_tokens")
        else:
            print(f"   ❌ Max tokens mismatch: Azure={azure_provider.default_max_tokens}, OpenAI={openai_provider.default_max_tokens}")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ Provider comparison test failed: {str(e)}")
        return False


def run_all_tests():
    """Run all Azure OpenAI integration tests."""
    print("🧪 Starting Azure OpenAI Integration Tests\n")
    
    tests = [
        ("Provider Factory Integration", test_azure_openai_in_provider_factory),
        ("Default Configuration", test_azure_openai_default_config),
        ("Configuration Override", test_azure_openai_config_override),
        ("Provider Consistency", test_azure_openai_provider_comparison),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print(f"\n🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All Azure OpenAI integration tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    if not success:
        sys.exit(1)
