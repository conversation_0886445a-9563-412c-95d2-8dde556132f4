# Suggested Commands

## Windows System Commands
- `dir` - List directory contents (equivalent to `ls` on Unix)
- `cd <directory>` - Change directory
- `copy <source> <dest>` - Copy files (equivalent to `cp` on Unix)
- `move <source> <dest>` - Move files (equivalent to `mv` on Unix)
- `del <file>` - Delete files (equivalent to `rm` on Unix)
- `findstr <pattern> <files>` - Search text in files (equivalent to `grep` on Unix)
- `where <command>` - Find command location (equivalent to `which` on Unix)

## Development Commands

### Environment Setup
```bash
# Copy environment template
copy .env.example .env

# Install Python dependencies
pip install -r requirements.txt
```

### Docker Operations
```bash
# Start all services (recommended)
docker-compose up -d

# Start specific services
docker-compose up -d db redis

# View logs
docker-compose logs -f app

# Stop services
docker-compose down

# Rebuild and start
docker-compose up --build -d
```

### Local Development
```bash
# Run application locally (after starting db and redis)
uvicorn app.main:app --reload

# Run with specific host/port
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### Testing
```bash
# Run all tests
pytest

# Run with verbose output
pytest -v

# Run specific test file
pytest test_api.py

# Run with coverage
pytest --cov=app

# Run API integration test
python test_api.py
python test_complete_system.py
```

### Database Operations
```bash
# Access PostgreSQL (when running in Docker)
docker-compose exec db psql -U user -d agent_db

# Access Redis CLI
docker-compose exec redis redis-cli
```

### Monitoring
```bash
# Monitor API performance
python monitor_api.py

# Check application health
curl http://localhost:8000/health

# View API documentation
# Open http://localhost:8000/docs in browser
```

### Git Operations
```bash
git status
git add .
git commit -m "Your commit message"
git push
git pull
```

## API Testing Commands
```bash
# Test registration
curl -X POST "http://localhost:8000/api/v1/auth/register" -H "Content-Type: application/json" -d '{"username": "testuser", "email": "<EMAIL>", "password": "securepassword"}'

# Test login
curl -X POST "http://localhost:8000/api/v1/auth/login" -H "Content-Type: application/x-www-form-urlencoded" -d "username=testuser&password=securepassword"

# Test with authentication (replace YOUR_JWT_TOKEN)
curl -X GET "http://localhost:8000/api/v1/sessions" -H "Authorization: Bearer YOUR_JWT_TOKEN"
```