# Task Completion Guidelines

## When a Task is Completed

### 1. Code Quality Checks
- **Lint the code**: Ensure code follows Python PEP 8 standards
- **Type checking**: Verify type hints are correct and comprehensive
- **Import organization**: Check imports are properly organized and unused imports removed
- **Docstrings**: Ensure functions and classes have proper documentation

### 2. Testing Requirements
- **Run existing tests**: Execute `pytest` to ensure no regressions
- **Add new tests**: Write tests for new functionality
- **Test coverage**: Aim for comprehensive test coverage
- **Integration testing**: Run `python test_api.py` and `python test_complete_system.py`

### 3. Database Considerations
- **Migration needed**: Check if database schema changes require Alembic migrations
- **Model relationships**: Verify SQLAlchemy relationships are properly defined
- **Data validation**: Ensure Pydantic schemas match database models

### 4. API Documentation
- **OpenAPI schemas**: Verify FastAPI auto-generates correct API docs at `/docs`
- **Response models**: Ensure all endpoints have proper response schemas
- **Error handling**: Check custom exceptions are properly handled

### 5. Configuration Updates
- **Environment variables**: Update `.env.example` if new config is added
- **Settings validation**: Ensure Pydantic settings validate correctly
- **Docker configuration**: Update `docker-compose.yml` if needed

### 6. Security Considerations
- **Authentication**: Verify JWT token handling is secure
- **Authorization**: Check proper access controls are in place
- **Input validation**: Ensure all user inputs are validated
- **Secrets management**: No hardcoded secrets in code

### 7. Performance and Monitoring
- **Database queries**: Check for N+1 queries and optimize if needed
- **Async operations**: Ensure proper async/await usage
- **Caching**: Verify Redis caching is working correctly
- **Logging**: Add appropriate logging for debugging

### 8. Deployment Readiness
- **Docker build**: Verify `docker-compose up --build` works
- **Health checks**: Ensure `/health` endpoint responds correctly
- **Environment compatibility**: Test in development environment

### 9. Code Review Checklist
- **Naming conventions**: Follow established naming patterns
- **Error messages**: Provide clear, helpful error messages
- **Code duplication**: Eliminate unnecessary code duplication
- **Dependencies**: Check if new dependencies are necessary

### 10. Final Validation
- **Manual testing**: Test the feature manually via API
- **WebSocket testing**: If applicable, test real-time functionality
- **Role-based testing**: Test with different AI roles if relevant
- **Edge cases**: Test error conditions and edge cases

## Commands to Run After Task Completion
```bash
# 1. Run tests
pytest -v

# 2. Run integration tests
python test_api.py
python test_complete_system.py

# 3. Check application startup
docker-compose up --build -d

# 4. Verify health
curl http://localhost:8000/health

# 5. Check API documentation
Open http://localhost:8000/docs
```