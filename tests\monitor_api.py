#!/usr/bin/env python3
"""
Simple monitoring script for the AI Agent System API.
"""
import asyncio
import httpx
import time
from datetime import datetime
import json

BASE_URL = "http://localhost:8000"

async def check_health():
    """Check the health endpoint."""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            start_time = time.time()
            response = await client.get(f"{BASE_URL}/health")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Health: OK ({response_time:.2f}s) - {data.get('status', 'unknown')}")
                return True
            else:
                print(f"❌ Health: FAIL ({response.status_code}) - {response_time:.2f}s")
                return False
    except Exception as e:
        print(f"❌ Health: ERROR - {str(e)}")
        return False

async def check_root():
    """Check the root endpoint."""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            start_time = time.time()
            response = await client.get(f"{BASE_URL}/")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                print(f"✅ Root: OK ({response_time:.2f}s)")
                return True
            else:
                print(f"❌ Root: FAIL ({response.status_code}) - {response_time:.2f}s")
                return False
    except Exception as e:
        print(f"❌ Root: ERROR - {str(e)}")
        return False

async def monitor_continuously():
    """Monitor the API continuously."""
    print(f"🔍 Starting continuous monitoring at {datetime.now()}")
    print("Press Ctrl+C to stop monitoring\n")
    
    try:
        while True:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] Checking API status...")
            
            health_ok = await check_health()
            root_ok = await check_root()
            
            if health_ok and root_ok:
                print("🟢 All systems operational\n")
            else:
                print("🔴 Some systems down\n")
            
            # Wait 30 seconds before next check
            await asyncio.sleep(30)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Monitoring stopped at {datetime.now()}")

async def single_check():
    """Perform a single health check."""
    print(f"🔍 Performing single health check at {datetime.now()}\n")
    
    health_ok = await check_health()
    root_ok = await check_root()
    
    if health_ok and root_ok:
        print("\n🟢 All systems operational")
    else:
        print("\n🔴 Some systems down")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        asyncio.run(monitor_continuously())
    else:
        asyncio.run(single_check())