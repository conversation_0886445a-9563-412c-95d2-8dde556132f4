"""
Sample functions for demonstrating AI function calling capabilities.

This module contains example functions that can be called by the AI
to demonstrate the function calling system.
"""

import json
import math
import datetime
from typing import Dict, Any, List

from app.services.function_executor import register_function


# Calculator functions
@register_function(
    name="calculate",
    schema={
        "type": "function",
        "function": {
            "name": "calculate",
            "description": "Perform basic mathematical calculations",
            "parameters": {
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "Mathematical expression to evaluate (e.g., '2 + 3 * 4')"
                    }
                },
                "required": ["expression"]
            }
        }
    }
)
def calculate(expression: str) -> Dict[str, Any]:
    """
    Safely evaluate a mathematical expression.
    
    Args:
        expression: Mathematical expression to evaluate
        
    Returns:
        Dictionary with result or error
    """
    try:
        # Only allow safe mathematical operations
        allowed_names = {
            k: v for k, v in math.__dict__.items() 
            if not k.startswith("__")
        }
        allowed_names.update({
            "abs": abs,
            "round": round,
            "min": min,
            "max": max,
            "sum": sum,
            "pow": pow
        })
        
        # Evaluate the expression safely
        result = eval(expression, {"__builtins__": {}}, allowed_names)
        
        return {
            "result": result,
            "expression": expression,
            "success": True
        }
    except Exception as e:
        return {
            "error": str(e),
            "expression": expression,
            "success": False
        }


# Date and time functions
@register_function(
    name="get_current_time",
    schema={
        "type": "function",
        "function": {
            "name": "get_current_time",
            "description": "Get the current date and time",
            "parameters": {
                "type": "object",
                "properties": {
                    "timezone": {
                        "type": "string",
                        "description": "Timezone (optional, defaults to UTC)",
                        "default": "UTC"
                    },
                    "format": {
                        "type": "string",
                        "description": "Date format (optional, defaults to ISO format)",
                        "default": "iso"
                    }
                },
                "required": []
            }
        }
    }
)
def get_current_time(timezone: str = "UTC", format: str = "iso") -> Dict[str, Any]:
    """
    Get the current date and time.
    
    Args:
        timezone: Timezone (currently only UTC supported)
        format: Output format
        
    Returns:
        Dictionary with current time information
    """
    try:
        now = datetime.datetime.utcnow()
        
        if format == "iso":
            formatted_time = now.isoformat() + "Z"
        elif format == "readable":
            formatted_time = now.strftime("%Y-%m-%d %H:%M:%S UTC")
        elif format == "timestamp":
            formatted_time = now.timestamp()
        else:
            formatted_time = now.isoformat() + "Z"
        
        return {
            "current_time": formatted_time,
            "timezone": timezone,
            "format": format,
            "success": True
        }
    except Exception as e:
        return {
            "error": str(e),
            "success": False
        }


# Text processing functions
@register_function(
    name="analyze_text",
    schema={
        "type": "function",
        "function": {
            "name": "analyze_text",
            "description": "Analyze text and provide statistics",
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Text to analyze"
                    },
                    "include_words": {
                        "type": "boolean",
                        "description": "Include word frequency analysis",
                        "default": False
                    }
                },
                "required": ["text"]
            }
        }
    }
)
def analyze_text(text: str, include_words: bool = False) -> Dict[str, Any]:
    """
    Analyze text and provide statistics.
    
    Args:
        text: Text to analyze
        include_words: Whether to include word frequency analysis
        
    Returns:
        Dictionary with text analysis results
    """
    try:
        # Basic statistics
        char_count = len(text)
        word_count = len(text.split())
        line_count = len(text.splitlines())
        
        # Character frequency
        char_freq = {}
        for char in text.lower():
            if char.isalpha():
                char_freq[char] = char_freq.get(char, 0) + 1
        
        result = {
            "character_count": char_count,
            "word_count": word_count,
            "line_count": line_count,
            "character_frequency": char_freq,
            "success": True
        }
        
        # Word frequency (optional)
        if include_words:
            words = text.lower().split()
            word_freq = {}
            for word in words:
                # Remove punctuation
                clean_word = ''.join(c for c in word if c.isalnum())
                if clean_word:
                    word_freq[clean_word] = word_freq.get(clean_word, 0) + 1
            
            # Get top 10 most frequent words
            top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
            result["word_frequency"] = dict(top_words)
        
        return result
        
    except Exception as e:
        return {
            "error": str(e),
            "success": False
        }


# Data processing functions
@register_function(
    name="process_json",
    schema={
        "type": "function",
        "function": {
            "name": "process_json",
            "description": "Process and validate JSON data",
            "parameters": {
                "type": "object",
                "properties": {
                    "json_string": {
                        "type": "string",
                        "description": "JSON string to process"
                    },
                    "operation": {
                        "type": "string",
                        "description": "Operation to perform: 'validate', 'pretty', 'minify'",
                        "enum": ["validate", "pretty", "minify"],
                        "default": "validate"
                    }
                },
                "required": ["json_string"]
            }
        }
    }
)
def process_json(json_string: str, operation: str = "validate") -> Dict[str, Any]:
    """
    Process and validate JSON data.
    
    Args:
        json_string: JSON string to process
        operation: Operation to perform
        
    Returns:
        Dictionary with processing results
    """
    try:
        # Parse JSON
        data = json.loads(json_string)
        
        result = {
            "valid": True,
            "success": True
        }
        
        if operation == "validate":
            result["message"] = "JSON is valid"
            result["data"] = data
        elif operation == "pretty":
            result["formatted"] = json.dumps(data, indent=2, ensure_ascii=False)
        elif operation == "minify":
            result["formatted"] = json.dumps(data, separators=(',', ':'))
        
        return result
        
    except json.JSONDecodeError as e:
        return {
            "valid": False,
            "error": str(e),
            "success": False
        }
    except Exception as e:
        return {
            "error": str(e),
            "success": False
        }


# System information function
@register_function(
    name="get_system_info",
    schema={
        "type": "function",
        "function": {
            "name": "get_system_info",
            "description": "Get basic system information",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    }
)
def get_system_info() -> Dict[str, Any]:
    """
    Get basic system information.
    
    Returns:
        Dictionary with system information
    """
    try:
        import platform
        import sys
        
        return {
            "python_version": sys.version,
            "platform": platform.platform(),
            "architecture": platform.architecture(),
            "processor": platform.processor(),
            "success": True
        }
    except Exception as e:
        return {
            "error": str(e),
            "success": False
        }
