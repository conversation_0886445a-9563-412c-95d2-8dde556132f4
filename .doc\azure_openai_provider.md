# Azure OpenAI Provider

The Azure OpenAI provider enables the AI Agent System to use Azure OpenAI Service as an LLM backend. This provider is fully compatible with the existing provider interface and can be used as a drop-in replacement for other providers.

## Features

- **Full Azure OpenAI API Support**: Complete integration with Azure OpenAI Service
- **Streaming Support**: Real-time response streaming for better user experience
- **Configuration Validation**: Comprehensive validation of required Azure OpenAI settings
- **Error Handling**: Robust error handling with meaningful error messages
- **Provider Interface Compatibility**: Seamless integration with the existing provider system

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```env
# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT=your_deployment_name

# Set Azure OpenAI as the default provider (optional)
LLM_PROVIDER=azure_openai
```

### Required Configuration Parameters

- **`AZURE_OPENAI_API_KEY`**: Your Azure OpenAI API key
- **`AZURE_OPENAI_ENDPOINT`**: Your Azure OpenAI resource endpoint URL
- **`AZURE_OPENAI_DEPLOYMENT`**: The name of your deployed model
- **`AZURE_OPENAI_API_VERSION`**: API version (default: `2024-02-15-preview`)

### Optional Configuration Parameters

- **`temperature`**: Controls randomness (default: 0.7)
- **`max_tokens`**: Maximum tokens in response (default: 2000)

## Usage

### Using the Provider Factory

```python
from app.llm.provider_factory import create_llm_provider

# Create Azure OpenAI provider with default settings
provider = create_llm_provider("azure_openai")

# Create with custom configuration
config = {
    "api_key": "your_api_key",
    "endpoint": "https://your-resource.openai.azure.com/",
    "deployment": "your-deployment",
    "temperature": 0.8,
    "max_tokens": 1500
}
provider = create_llm_provider("azure_openai", config)
```

### Direct Provider Creation

```python
from app.llm.azure_openai_provider import create_azure_openai_provider

# Create with custom configuration
config = {
    "api_key": "your_api_key",
    "endpoint": "https://your-resource.openai.azure.com/",
    "deployment": "your-deployment",
    "api_version": "2024-02-15-preview"
}
provider = create_azure_openai_provider(config)
```

### Generating Responses

```python
from app.llm.base import ChatMessage

# Prepare messages
messages = [
    ChatMessage(role="system", content="You are a helpful assistant."),
    ChatMessage(role="user", content="Hello, how are you?")
]

# Generate complete response
response = await provider.generate_response(messages)
print(response.content)

# Generate streaming response
async for chunk in provider.generate_stream(messages):
    print(chunk.content, end="", flush=True)
    if chunk.is_complete:
        break
```

## Azure OpenAI Setup

### 1. Create Azure OpenAI Resource

1. Go to the [Azure Portal](https://portal.azure.com/)
2. Create a new Azure OpenAI resource
3. Note the endpoint URL and API key

### 2. Deploy a Model

1. In your Azure OpenAI resource, go to "Model deployments"
2. Deploy a model (e.g., GPT-4, GPT-3.5-turbo)
3. Note the deployment name

### 3. Configure the Application

Update your `.env` file with the Azure OpenAI configuration:

```env
AZURE_OPENAI_API_KEY=your_api_key_from_azure
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=your_deployment_name
```

## Docker Configuration

The Azure OpenAI provider is automatically configured in Docker Compose. Make sure to set the environment variables in your `.env` file:

```yaml
# docker-compose.yml (already configured)
environment:
  - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
  - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
  - AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION}
  - AZURE_OPENAI_DEPLOYMENT=${AZURE_OPENAI_DEPLOYMENT}
```

## Testing

Run the Azure OpenAI provider tests to verify your configuration:

```bash
# Test provider functionality
python test_azure_openai_provider.py

# Test integration with the main system
python test_azure_openai_integration.py
```

## Troubleshooting

### Common Issues

1. **"Azure OpenAI API key is required"**
   - Ensure `AZURE_OPENAI_API_KEY` is set in your environment

2. **"Azure OpenAI endpoint is required"**
   - Ensure `AZURE_OPENAI_ENDPOINT` is set and formatted correctly

3. **"Azure OpenAI deployment name is required"**
   - Ensure `AZURE_OPENAI_DEPLOYMENT` matches your deployed model name

4. **API Connection Errors**
   - Verify your Azure OpenAI resource is active
   - Check that your API key has the correct permissions
   - Ensure your deployment is running

### Validation

Use the `validate_connection()` method to test your configuration:

```python
provider = create_llm_provider("azure_openai")
is_valid = await provider.validate_connection()
if is_valid:
    print("✅ Azure OpenAI connection is working")
else:
    print("❌ Azure OpenAI connection failed")
```

## Comparison with Other Providers

| Feature | Azure OpenAI | OpenAI | DeepSeek |
|---------|--------------|--------|----------|
| API Compatibility | Azure OpenAI API | OpenAI API | OpenAI-compatible |
| Streaming | ✅ | ✅ | ✅ |
| Custom Endpoints | ✅ | ❌ | ✅ |
| Enterprise Features | ✅ | ❌ | ❌ |
| Data Residency | ✅ | ❌ | ❌ |

## Migration from OpenAI

To migrate from the OpenAI provider to Azure OpenAI:

1. Set up your Azure OpenAI resource and deployment
2. Update your `.env` file with Azure OpenAI configuration
3. Change `LLM_PROVIDER=azure_openai` in your environment
4. Restart the application

The migration is seamless as both providers use the same interface.
