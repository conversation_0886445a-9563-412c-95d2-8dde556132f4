"""
Function execution service for handling AI function calls.

This service manages the execution of functions called by the AI,
including validation, execution, and error handling.
"""

import json
import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

from app.core.exceptions import FunctionExecutionError

logger = logging.getLogger(__name__)


@dataclass
class FunctionResult:
    """Result of a function execution."""
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: Optional[float] = None


class FunctionRegistry:
    """Registry for managing available functions."""
    
    def __init__(self):
        self._functions: Dict[str, Callable] = {}
        self._schemas: Dict[str, Dict[str, Any]] = {}
    
    def register_function(
        self, 
        name: str, 
        func: Callable, 
        schema: Dict[str, Any]
    ) -> None:
        """
        Register a function with its schema.
        
        Args:
            name: Function name
            func: Callable function
            schema: OpenAI function schema
        """
        self._functions[name] = func
        self._schemas[name] = schema
        logger.info(f"Registered function: {name}")
    
    def get_function(self, name: str) -> Optional[Callable]:
        """Get a function by name."""
        return self._functions.get(name)
    
    def get_schema(self, name: str) -> Optional[Dict[str, Any]]:
        """Get a function schema by name."""
        return self._schemas.get(name)
    
    def get_all_schemas(self) -> List[Dict[str, Any]]:
        """Get all function schemas."""
        return list(self._schemas.values())
    
    def list_functions(self) -> List[str]:
        """List all registered function names."""
        return list(self._functions.keys())


class FunctionExecutor:
    """Service for executing AI function calls."""
    
    def __init__(self):
        self.registry = FunctionRegistry()
    
    def register_function(
        self, 
        name: str, 
        func: Callable, 
        schema: Dict[str, Any]
    ) -> None:
        """Register a function for execution."""
        self.registry.register_function(name, func, schema)
    
    async def execute_function(
        self, 
        name: str, 
        arguments: str
    ) -> FunctionResult:
        """
        Execute a function call.
        
        Args:
            name: Function name
            arguments: JSON string of function arguments
            
        Returns:
            FunctionResult: Result of the function execution
        """
        import time
        start_time = time.time()
        
        try:
            # Get the function
            func = self.registry.get_function(name)
            if not func:
                return FunctionResult(
                    success=False,
                    error=f"Function '{name}' not found"
                )
            
            # Parse arguments
            try:
                args = json.loads(arguments) if arguments else {}
            except json.JSONDecodeError as e:
                return FunctionResult(
                    success=False,
                    error=f"Invalid JSON arguments: {str(e)}"
                )
            
            # Execute the function
            try:
                if hasattr(func, '__call__'):
                    # Handle both sync and async functions
                    import asyncio
                    if asyncio.iscoroutinefunction(func):
                        result = await func(**args)
                    else:
                        result = func(**args)
                else:
                    return FunctionResult(
                        success=False,
                        error=f"'{name}' is not callable"
                    )
                
                execution_time = time.time() - start_time
                
                return FunctionResult(
                    success=True,
                    result=result,
                    execution_time=execution_time
                )
                
            except Exception as e:
                logger.error(f"Error executing function '{name}': {str(e)}")
                return FunctionResult(
                    success=False,
                    error=f"Function execution error: {str(e)}",
                    execution_time=time.time() - start_time
                )
                
        except Exception as e:
            logger.error(f"Unexpected error in function execution: {str(e)}")
            return FunctionResult(
                success=False,
                error=f"Unexpected error: {str(e)}",
                execution_time=time.time() - start_time
            )
    
    def get_available_functions(self) -> List[Dict[str, Any]]:
        """Get all available function schemas for the LLM."""
        return self.registry.get_all_schemas()
    
    def get_functions_for_tools(self, tool_names: List[str]) -> List[Dict[str, Any]]:
        """
        Get function schemas for specific tools.
        
        Args:
            tool_names: List of tool names to get functions for
            
        Returns:
            List of function schemas
        """
        schemas = []
        for tool_name in tool_names:
            schema = self.registry.get_schema(tool_name)
            if schema:
                schemas.append(schema)
        return schemas

    def get_available_functions(self) -> List[Dict[str, Any]]:
        """Get all available function schemas."""
        return [schema for schema in self.registry.schemas.values()]


# Global function executor instance
function_executor = FunctionExecutor()


def register_function(name: str, schema: Dict[str, Any]):
    """
    Decorator for registering functions.
    
    Args:
        name: Function name
        schema: OpenAI function schema
    """
    def decorator(func: Callable):
        function_executor.register_function(name, func, schema)
        return func
    return decorator
