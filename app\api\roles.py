from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.models.base import get_db
from app.models.user import User
from app.schemas.role import (
    RoleCreate, 
    RoleUpdate, 
    RoleResponse, 
    RoleListResponse,
    RoleSyncResponse
)
from app.services.role_service import RoleService
from app.core.dependencies import get_current_active_user
from app.core.exceptions import NotFoundError, ConflictError, ValidationError
from app.agent.role_loader import role_loader

router = APIRouter()


@router.get("/", response_model=RoleListResponse)
async def list_roles(
    active_only: bool = Query(True, description="Filter by active roles only"),
    db: Session = Depends(get_db)
):
    """
    List all roles.
    
    Args:
        active_only: Whether to return only active roles
        db: Database session
        
    Returns:
        RoleListResponse: List of roles
    """
    role_service = RoleService(db)
    roles = role_service.list_roles(active_only=active_only)
    
    role_responses = []
    for role in roles:
        role_responses.append(RoleResponse(
            id=str(role.id),
            name=role.name,
            display_name=role.display_name,
            description=role.description or "",
            system_prompt=role.system_prompt,
            tools=role.tools or [],
            config=role.config or {},
            is_active=role.is_active,
            created_at=role.created_at,
            updated_at=role.updated_at
        ))
    
    return RoleListResponse(roles=role_responses, total=len(role_responses))


@router.get("/{role_name}", response_model=RoleResponse)
async def get_role(
    role_name: str,
    db: Session = Depends(get_db)
):
    """
    Get a specific role by name.
    
    Args:
        role_name: Name of the role
        db: Database session
        
    Returns:
        RoleResponse: Role details
        
    Raises:
        HTTPException: If role not found
    """
    role_service = RoleService(db)
    role = role_service.get_role_by_name(role_name)
    
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Role '{role_name}' not found"
        )
    
    return RoleResponse(
        id=str(role.id),
        name=role.name,
        display_name=role.display_name,
        description=role.description or "",
        system_prompt=role.system_prompt,
        tools=role.tools or [],
        config=role.config or {},
        is_active=role.is_active,
        created_at=role.created_at,
        updated_at=role.updated_at
    )


@router.post("/", response_model=RoleResponse, status_code=status.HTTP_201_CREATED)
async def create_role(
    role_data: RoleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new role (admin only).
    
    Args:
        role_data: Role creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        RoleResponse: Created role
        
    Raises:
        HTTPException: If role creation fails
    """
    role_service = RoleService(db)
    
    try:
        role = role_service.create_role(role_data)
        
        # Reload roles in the role loader to include the new role
        role_loader.set_database_session(db)
        
        return RoleResponse(
            id=str(role.id),
            name=role.name,
            display_name=role.display_name,
            description=role.description or "",
            system_prompt=role.system_prompt,
            tools=role.tools or [],
            config=role.config or {},
            is_active=role.is_active,
            created_at=role.created_at,
            updated_at=role.updated_at
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.put("/{role_name}", response_model=RoleResponse)
async def update_role(
    role_name: str,
    role_data: RoleUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update an existing role (admin only).
    
    Args:
        role_name: Name of the role to update
        role_data: Role update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        RoleResponse: Updated role
        
    Raises:
        HTTPException: If role update fails
    """
    role_service = RoleService(db)
    
    try:
        role = role_service.update_role(role_name, role_data)
        
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role '{role_name}' not found"
            )
        
        # Reload roles in the role loader to reflect changes
        role_loader.set_database_session(db)
        
        return RoleResponse(
            id=str(role.id),
            name=role.name,
            display_name=role.display_name,
            description=role.description or "",
            system_prompt=role.system_prompt,
            tools=role.tools or [],
            config=role.config or {},
            is_active=role.is_active,
            created_at=role.created_at,
            updated_at=role.updated_at
        )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.delete("/{role_name}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(
    role_name: str,
    hard_delete: bool = Query(False, description="Permanently delete role instead of soft delete"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a role (admin only).
    
    Args:
        role_name: Name of the role to delete
        hard_delete: Whether to permanently delete the role
        db: Database session
        current_user: Current authenticated user
        
    Raises:
        HTTPException: If role deletion fails
    """
    role_service = RoleService(db)
    
    try:
        if hard_delete:
            role_service.hard_delete_role(role_name)
        else:
            role_service.delete_role(role_name)
        
        # Reload roles in the role loader to reflect changes
        role_loader.set_database_session(db)
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.post("/sync", response_model=RoleSyncResponse)
async def sync_roles_from_yaml(
    overwrite_existing: bool = Query(False, description="Overwrite existing roles with YAML data"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Sync roles from YAML configuration to database (admin only).
    
    Args:
        overwrite_existing: Whether to overwrite existing roles
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        RoleSyncResponse: Sync operation results
        
    Raises:
        HTTPException: If sync operation fails
    """
    role_service = RoleService(db)
    
    try:
        sync_result = role_service.sync_yaml_to_database(overwrite_existing=overwrite_existing)
        
        # Reload roles in the role loader to reflect changes
        role_loader.set_database_session(db)
        
        return sync_result
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except FileNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
