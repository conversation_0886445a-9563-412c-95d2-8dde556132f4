from typing import Dict, Any, Optional
from app.embedding.base import Embedding<PERSON>rovider, EmbeddingProviderFactory
from app.embedding.openai_provider import create_openai_embedding_provider
from app.embedding.azure_openai_provider import create_azure_openai_embedding_provider
from app.core.config import settings
from app.core.exceptions import LLMError


def create_embedding_provider(
    provider_name: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None
) -> EmbeddingProvider:
    """
    Create an embedding provider based on configuration.
    
    Args:
        provider_name: Optional provider name override
        config: Optional configuration dictionary
        
    Returns:
        EmbeddingProvider: Configured embedding provider instance
        
    Raises:
        LLMError: If provider creation fails
    """
    # Use provided provider name or default from settings
    provider = provider_name or settings.embedding_provider
    
    # Default configuration if none provided
    if config is None:
        config = {}
    
    try:
        if provider == "openai":
            # Create OpenAI embedding provider
            openai_config = {
                "model": settings.openai_embedding_model,
                "api_key": settings.openai_api_key,
                **config  # Override with any provided config
            }
            return create_openai_embedding_provider(openai_config)

        elif provider == "azure_openai":
            # Create Azure OpenAI embedding provider
            azure_openai_config = {
                "deployment": settings.azure_openai_embedding_deployment,
                "api_key": settings.azure_openai_api_key,
                "endpoint": settings.azure_openai_endpoint,
                "api_version": settings.azure_openai_api_version,
                "model": settings.azure_openai_embedding_model,
                **config  # Override with any provided config
            }
            return create_azure_openai_embedding_provider(azure_openai_config)

        else:
            # Try to use the factory registry
            try:
                return EmbeddingProviderFactory.create_provider(provider, config)
            except ValueError:
                raise LLMError(f"Unknown embedding provider: {provider}")
                
    except Exception as e:
        if isinstance(e, LLMError):
            raise e
        raise LLMError(f"Failed to create {provider} embedding provider: {str(e)}")


def get_default_embedding_provider_config(provider_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Get default configuration for an embedding provider.
    
    Args:
        provider_name: Optional provider name
        
    Returns:
        Dict[str, Any]: Default configuration
    """
    provider = provider_name or settings.embedding_provider
    
    if provider == "openai":
        return {
            "model": settings.openai_embedding_model,
            "api_key": settings.openai_api_key
        }
    elif provider == "azure_openai":
        return {
            "deployment": settings.azure_openai_embedding_deployment,
            "api_key": settings.azure_openai_api_key,
            "endpoint": settings.azure_openai_endpoint,
            "api_version": settings.azure_openai_api_version,
            "model": settings.azure_openai_embedding_model
        }
    else:
        return {}


def list_available_embedding_providers() -> Dict[str, str]:
    """
    List all available embedding providers with descriptions.
    
    Returns:
        Dict[str, str]: Provider names mapped to descriptions
    """
    providers = {
        "openai": "OpenAI embedding models (text-embedding-3-small, text-embedding-3-large, etc.)",
        "azure_openai": "Azure OpenAI embedding models with enterprise features"
    }
    
    # Add any registered providers from the factory
    for provider_name in EmbeddingProviderFactory.list_providers():
        if provider_name not in providers:
            providers[provider_name] = f"Custom provider: {provider_name}"
    
    return providers
