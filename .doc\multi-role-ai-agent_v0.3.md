# Multi-Role AI Agent v0.3 - Database-Enhanced Role Loading

## 🎯 Overview

This document outlines the implementation plan for enhancing the multi-role AI agent system to support loading roles from both YAML configuration files and a PostgreSQL database. The key enhancement is that database-stored roles will override YAML roles when there are naming conflicts, providing greater flexibility in role management.

## 🏗️ Architecture Design

### Role Loading Flow

```mermaid
graph TB
    subgraph "Role Loading Process"
        A[Start] --> B[Load YAML Roles]
        B --> C[Load Database Roles]
        C --> D[Merge Roles<br/>DB overrides YAML]
        D --> E[Validate Tools]
        E --> F[Filter Invalid Tools<br/>Log Warnings]
        F --> G[Final Role Registry]
    end
    
    subgraph "Data Sources"
        H[roles/default_roles.yaml] --> B
        I[(PostgreSQL Database)] --> C
    end
    
    subgraph "Priority Example"
        J[YAML: teacher role] -.->|Overridden by| K[DB: teacher role]
        L[YAML: programmer role] --> M[Final: programmer<br/>from YAML]
        K --> N[Final: teacher<br/>from DB]
    end
```

## 📊 Database Schema

### Roles Table Structure

```mermaid
erDiagram
    roles {
        uuid id PK "Primary key"
        string(50) name UK "Unique role identifier"
        string(100) display_name "User-friendly name"
        text description "Role description"
        text system_prompt "System prompt for LLM"
        jsonb tools "Array of tool names"
        jsonb config "Role configuration (temperature, max_tokens, etc.)"
        boolean is_active "Whether role is available"
        timestamp created_at "Creation timestamp"
        timestamp updated_at "Last update timestamp"
    }
```

## 🔧 Implementation Plan

### Phase 1: Database Setup

1. **Create Role Model** (`app/models/role.py`)
   ```python
   from sqlalchemy import Column, String, Text, Boolean, DateTime, JSON
   from sqlalchemy.dialects.postgresql import UUID
   import uuid
   from datetime import datetime
   from .base import Base

   class Role(Base):
       __tablename__ = "roles"
       
       id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
       name = Column(String(50), unique=True, nullable=False, index=True)
       display_name = Column(String(100), nullable=False)
       description = Column(Text)
       system_prompt = Column(Text, nullable=False)
       tools = Column(JSON, default=list)
       config = Column(JSON, default=dict)
       is_active = Column(Boolean, default=True)
       created_at = Column(DateTime, default=datetime.utcnow)
       updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
   ```

2. **Create Database Migration**
   - Use Alembic to create migration for roles table
   - Include indexes on `name` and `is_active` fields

### Phase 2: Enhanced Role Loader

1. **Modify RoleLoader** (`app/agent/role_loader.py`)
   ```python
   class RoleLoader:
       def __init__(self, config_path: str = None, db_session = None):
           self.config_path = config_path or settings.roles_config_path
           self.db_session = db_session
           self._roles: Dict[str, RoleConfig] = {}
           self._available_tools: List[str] = []
           self._load_all_roles()
       
       def _load_all_roles(self):
           """Load roles from YAML first, then database (DB overrides YAML)"""
           # 1. Load YAML roles as base
           self._load_yaml_roles()
           
           # 2. Load and merge database roles
           if self.db_session:
               self._load_database_roles()
           
           # 3. Validate tools for all roles
           self._validate_all_tools()
       
       def _load_database_roles(self):
           """Load roles from database, overriding YAML roles with same name"""
           try:
               db_roles = self.db_session.query(Role).filter(Role.is_active == True).all()
               for db_role in db_roles:
                   role_config = RoleConfig(
                       display_name=db_role.display_name,
                       description=db_role.description,
                       system_prompt=db_role.system_prompt,
                       tools=db_role.tools,
                       config=db_role.config,
                       is_active=db_role.is_active
                   )
                   # Override existing YAML role or add new
                   self._roles[db_role.name] = role_config
                   logger.info(f"Loaded database role: {db_role.name}")
           except Exception as e:
               logger.error(f"Error loading database roles: {str(e)}")
       
       def _validate_all_tools(self):
           """Validate and filter tools for each role"""
           for role_name, role_config in self._roles.items():
               valid_tools = []
               for tool in role_config.tools:
                   if tool in self._available_tools:
                       valid_tools.append(tool)
                   else:
                       logger.warning(f"Tool '{tool}' not found for role '{role_name}', skipping")
               role_config.tools = valid_tools
   ```

### Phase 3: Role Service Layer

1. **Create Role Service** (`app/services/role_service.py`)
   ```python
   from typing import List, Optional
   from sqlalchemy.orm import Session
   from app.models.role import Role
   from app.schemas.role import RoleCreate, RoleUpdate

   class RoleService:
       def __init__(self, db: Session):
           self.db = db
       
       def create_role(self, role_data: RoleCreate) -> Role:
           """Create a new role (admin only)"""
           db_role = Role(**role_data.dict())
           self.db.add(db_role)
           self.db.commit()
           self.db.refresh(db_role)
           return db_role
       
       def update_role(self, role_name: str, role_data: RoleUpdate) -> Optional[Role]:
           """Update existing role (admin only)"""
           role = self.db.query(Role).filter(Role.name == role_name).first()
           if role:
               for key, value in role_data.dict(exclude_unset=True).items():
                   setattr(role, key, value)
               self.db.commit()
               self.db.refresh(role)
           return role
       
       def delete_role(self, role_name: str) -> bool:
           """Soft delete a role by setting is_active to False"""
           role = self.db.query(Role).filter(Role.name == role_name).first()
           if role:
               role.is_active = False
               self.db.commit()
               return True
           return False
   ```

### Phase 4: Integration Updates

1. **Update Agent Factory** (`app/agent/factory.py`)
   - Pass database session to RoleLoader
   - Ensure proper dependency injection

2. **Update Dependencies** (`app/core/dependencies.py`)
   - Create dependency for RoleLoader with database session
   - Cache role loader instance for performance

## 📦 Updated File Structure

```
app/
├── models/
│   ├── __init__.py
│   ├── base.py
│   ├── user.py
│   ├── session.py
│   ├── message.py
│   └── role.py              # NEW: Role database model
├── services/
│   ├── __init__.py          # NEW
│   └── role_service.py      # NEW: Role management service
├── schemas/
│   ├── __init__.py
│   ├── auth.py
│   ├── chat.py
│   ├── session.py
│   └── role.py              # NEW: Role Pydantic schemas
├── agent/
│   ├── role_loader.py       # MODIFIED: Enhanced with DB support
│   └── factory.py           # MODIFIED: Updated integration
├── core/
│   └── dependencies.py      # MODIFIED: Add role loader dependency
└── migrations/
    └── versions/
        └── xxx_add_roles_table.py  # NEW: Alembic migration
```

## 🛡️ Tool Validation Strategy

1. **Load-Time Validation**
   - Check each role's tools against system's available tools
   - Filter out invalid tools automatically
   - Log warnings for each invalid tool found

2. **Graceful Degradation**
   - Roles remain functional with reduced tool set
   - System continues to operate even if some tools are missing
   - Clear logging for troubleshooting

## ⚙️ Configuration Requirements

1. **Environment Variables**
   ```env
   # Existing
   DATABASE_URL=postgresql://user:password@localhost:5432/dbname
   
   # New (optional)
   ROLE_LOADING_LOG_LEVEL=INFO
   ENABLE_DATABASE_ROLES=true
   ```

2. **Logging Configuration**
   - Add specific logger for role loading: `app.agent.role_loader`
   - Log levels: INFO for successful loads, WARNING for tool issues, ERROR for failures

## 🧪 Testing Strategy

1. **Unit Tests**
   - Role model CRUD operations
   - RoleLoader with mock database
   - Tool validation logic

2. **Integration Tests**
   - YAML + Database role loading
   - Override behavior verification
   - Tool filtering functionality

3. **Test Scenarios**
   - Database role overrides YAML role
   - Invalid tools are filtered
   - Database connection failure fallback
   - Performance with many roles

## 📈 Benefits

- **Dynamic Role Management**: Roles can be updated without code deployment
- **Override Capability**: Database roles take precedence for easy customization
- **Tool Safety**: Invalid tools are automatically filtered with clear logging
- **Backward Compatibility**: System works with YAML-only if database is unavailable
- **Administrator Control**: Only admins can manage database roles

## ⚠️ Important Notes

1. **No User Role Creation**: Regular users cannot create or modify roles
2. **Admin Interface**: Admin API endpoints are NOT included in v0.3 scope
3. **Priority Rule**: Database roles ALWAYS override YAML roles with same name
4. **Tool Validation**: Happens at load time, not at runtime for performance
5. **Existing Sessions**: Will continue using their assigned roles even if updated

## 🚀 Migration Path

1. **Initial Deployment**
   - Run Alembic migration to create roles table
   - Optionally seed database with current YAML roles
   - System continues using YAML roles until DB roles are added

2. **Adding Database Roles**
   - Use database management tools or scripts
   - Add roles one at a time
   - Test override behavior

3. **Maintenance**
   - Keep YAML file as fallback/documentation
   - Use database for production role customization
   - Monitor logs for tool validation warnings