import yaml
import os
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
from sqlalchemy.orm import Session
import logging

from app.core.config import settings
from app.core.exceptions import ValidationError

logger = logging.getLogger(__name__)


class RoleConfig(BaseModel):
    """Role configuration model."""
    display_name: str
    description: str
    system_prompt: str
    tools: List[str]
    config: Dict[str, Any]
    is_active: bool = True


class RoleLoader:
    """Loads and manages AI role configurations from database with YAML fallback."""

    def __init__(self, config_path: str = None, db_session: Optional[Session] = None):
        self.config_path = config_path or settings.roles_config_path
        self.db_session = db_session
        self._roles: Dict[str, RoleConfig] = {}
        self._load_all_roles()
    
    def _load_all_roles(self):
        """Load roles from database first, then fallback to YAML if no roles in database."""
        try:
            # Try to load from database first
            if self.db_session:
                db_roles_loaded = self._load_database_roles()
                if db_roles_loaded:
                    logger.info(f"Loaded {len(self._roles)} roles from database")
                    return

            # Fallback to YAML if no database session or no roles in database
            self._load_yaml_roles()
            logger.info(f"Loaded {len(self._roles)} roles from YAML (fallback)")

        except Exception as e:
            logger.error(f"Error loading roles: {str(e)}")
            raise ValidationError(f"Error loading roles: {str(e)}")

    def _load_database_roles(self) -> bool:
        """Load roles from database. Returns True if any roles were loaded."""
        try:
            # Import here to avoid circular imports
            from app.models.role import Role

            db_roles = self.db_session.query(Role).filter(Role.is_active == True).all()

            if not db_roles:
                logger.info("No active roles found in database")
                return False

            for db_role in db_roles:
                try:
                    role_config = RoleConfig(
                        display_name=db_role.display_name,
                        description=db_role.description or "",
                        system_prompt=db_role.system_prompt,
                        tools=db_role.tools or [],
                        config=db_role.config or {},
                        is_active=db_role.is_active
                    )
                    self._roles[db_role.name] = role_config
                    logger.debug(f"Loaded database role: {db_role.name}")
                except Exception as e:
                    logger.error(f"Error loading database role '{db_role.name}': {str(e)}")
                    continue

            return len(self._roles) > 0

        except Exception as e:
            logger.error(f"Error loading database roles: {str(e)}")
            return False

    def _load_yaml_roles(self):
        """Load roles from YAML configuration file."""
        try:
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"Roles configuration file not found: {self.config_path}")

            with open(self.config_path, 'r', encoding='utf-8') as file:
                roles_data = yaml.safe_load(file)

            if not roles_data:
                raise ValidationError("No roles found in configuration file")

            for role_name, role_data in roles_data.items():
                try:
                    self._roles[role_name] = RoleConfig(**role_data)
                    logger.debug(f"Loaded YAML role: {role_name}")
                except Exception as e:
                    logger.error(f"Invalid configuration for role '{role_name}': {str(e)}")
                    continue

        except FileNotFoundError as e:
            raise ValidationError(f"Roles configuration file not found: {str(e)}")
        except yaml.YAMLError as e:
            raise ValidationError(f"Invalid YAML in roles configuration: {str(e)}")
        except Exception as e:
            raise ValidationError(f"Error loading YAML roles configuration: {str(e)}")
    
    def get_role(self, role_name: str) -> Optional[RoleConfig]:
        """Get a specific role configuration."""
        return self._roles.get(role_name)
    
    def get_active_roles(self) -> Dict[str, RoleConfig]:
        """Get all active roles."""
        return {name: config for name, config in self._roles.items() if config.is_active}
    
    def list_role_names(self) -> List[str]:
        """Get list of all active role names."""
        return list(self.get_active_roles().keys())
    
    def role_exists(self, role_name: str) -> bool:
        """Check if a role exists and is active."""
        role = self._roles.get(role_name)
        return role is not None and role.is_active
    
    def reload_roles(self):
        """Reload roles from database or configuration file."""
        self._roles.clear()
        self._load_all_roles()

    def set_database_session(self, db_session: Session):
        """Set database session for loading roles from database."""
        self.db_session = db_session
        # Reload roles with new database session
        self.reload_roles()
    
    def validate_role_tools(self, role_name: str, available_tools: List[str]) -> bool:
        """Validate that all tools required by a role are available."""
        role = self.get_role(role_name)
        if not role:
            return False
        
        return all(tool in available_tools for tool in role.tools)


# Global role loader instance (will be initialized with database session later)
role_loader = RoleLoader()