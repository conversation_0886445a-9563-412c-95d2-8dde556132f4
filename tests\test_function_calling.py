"""
Test script for function calling functionality.

This script tests the complete function calling implementation
including function registration, execution, and AI integration.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.function_executor import function_executor
from app.services.sample_functions import *  # Import to register functions
from app.llm.base import ChatMessage, FunctionCall, ToolCall
from app.llm.azure_openai_provider import AzureOpenAIProvider
from app.agent.factory import AgentFactory
from app.agent.role_loader import role_loader
from app.core.config import settings


async def test_function_executor():
    """Test the function executor directly."""
    print("=== Testing Function Executor ===")
    
    # Test calculator function
    print("\n1. Testing calculator function:")
    result = await function_executor.execute_function("calculate", '{"expression": "2 + 3 * 4"}')
    print(f"Result: {result.result}")
    print(f"Success: {result.success}")
    
    # Test current time function
    print("\n2. Testing current time function:")
    result = await function_executor.execute_function("get_current_time", '{"format": "readable"}')
    print(f"Result: {result.result}")
    print(f"Success: {result.success}")
    
    # Test text analysis function
    print("\n3. Testing text analysis function:")
    result = await function_executor.execute_function(
        "analyze_text", 
        '{"text": "Hello world! This is a test.", "include_words": true}'
    )
    print(f"Result: {result.result}")
    print(f"Success: {result.success}")
    
    # Test error handling
    print("\n4. Testing error handling:")
    result = await function_executor.execute_function("nonexistent_function", '{}')
    print(f"Error: {result.error}")
    print(f"Success: {result.success}")


async def test_llm_function_calling():
    """Test LLM function calling with OpenAI provider."""
    print("\n=== Testing LLM Function Calling ===")
    
    # Check if OpenAI API key is available
    if not settings.openai_api_key:
        print("OpenAI API key not found. Skipping LLM tests.")
        return
    
    try:
        # Create OpenAI provider
        provider = AzureOpenAIProvider({
            "api_key": "LGxHqNYJ0rCMCYjIwgGh4aFIanja9WIbFCnP36ZF2BZJkdAxJS5HJQQJ99BFAC4f1cMXJ3w3AAABACOGIW6f",
            "endpoint": "https://xapa-open-ai.openai.azure.com/",
            "api_version": "2025-01-01-preview",
            "deployment": "gpt-4.1",
            "temperature": 0.7,
            "max_tokens": 1000
        })
        
        # Test connection
        print("\n1. Testing LLM connection:")
        is_valid = await provider.validate_connection()
        print(f"Connection valid: {is_valid}")
        
        if not is_valid:
            print("LLM connection failed. Skipping LLM tests.")
            return
        
        # Get available functions
        tools = function_executor.get_available_functions()
        print(f"\n2. Available functions: {len(tools)}")
        for tool in tools[:3]:  # Show first 3
            print(f"   - {tool['function']['name']}: {tool['function']['description']}")
        
        # Test function calling
        print("\n3. Testing function calling:")
        messages = [
            ChatMessage(role="user", content="Calculate 15 * 7 + 3")
        ]
        
        response = await provider.generate_response(messages, tools=tools)
        print(f"Response content: {response.content}")
        print(f"Function call: {response.function_call}")
        print(f"Tool calls: {response.tool_calls}")
        
    except Exception as e:
        print(f"LLM test error: {str(e)}")


async def test_agent_function_calling():
    """Test function calling through the agent system."""
    print("\n=== Testing Agent Function Calling ===")
    
    try:
        # Create a test role with tools
        test_role_config = {
            "display_name": "Test Assistant",
            "description": "A test assistant with function calling capabilities",
            "system_prompt": "You are a helpful assistant that can perform calculations and analyze text. Use the available functions when appropriate.",
            "tools": ["calculate", "analyze_text", "get_current_time"],
            "is_active": True,
            "config": {
                "temperature": 0.1,
                "max_tokens": 1000
            }
        }
        
        # Register the test role
        role_loader._roles["test_assistant"] = type('RoleConfig', (), test_role_config)()
        
        # Create agent factory
        agent_factory = AgentFactory()
        
        # Create agent
        print("\n1. Creating test agent:")
        agent = await agent_factory.create_agent("test_session", "test_assistant")
        print(f"Agent created with role: {agent.role_config.display_name}")
        
        # Test function calling through agent
        print("\n2. Testing calculation through agent:")
        response = await agent.generate_response("What is 25 * 8 + 12?")
        print(f"Agent response: {response}")
        
        print("\n3. Testing text analysis through agent:")
        response = await agent.generate_response("Analyze this text: 'The quick brown fox jumps over the lazy dog.'")
        print(f"Agent response: {response}")
        
    except Exception as e:
        print(f"Agent test error: {str(e)}")


async def main():
    """Run all tests."""
    print("Starting Function Calling Tests")
    print("=" * 50)
    
    # Test function executor
    await test_function_executor()
    
    # Test LLM function calling
    await test_llm_function_calling()
    
    # Test agent function calling
    await test_agent_function_calling()
    
    print("\n" + "=" * 50)
    print("Function Calling Tests Complete")


if __name__ == "__main__":
    asyncio.run(main())
