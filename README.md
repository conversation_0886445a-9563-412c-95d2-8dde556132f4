# Multi-User, Multi-Session, Multi-Role AI Agent System

A comprehensive AI agent system built with FastAPI that supports multiple users, sessions, and AI roles with real-time communication capabilities.

## 🚀 Features

- **Multi-User Support**: User registration, authentication with JWT tokens
- **Multi-Session Management**: Users can create, name, and manage multiple chat sessions
- **Multi-Role AI System**: Predefined AI roles (Teacher, Programmer, Doctor, etc.) with specialized capabilities
- **Real-time Communication**: WebSocket support for streaming responses
- **Session Persistence**: Redis caching with PostgreSQL storage
- **Role-based Tools**: Each AI role has access to specific tools and capabilities
- **Embedding Support**: Text embeddings, similarity search, and clustering capabilities
- **Secure Authentication**: JWT-based authentication with bcrypt password hashing

## 🏗️ Architecture

### Tech Stack
- **Backend**: Python 3.11+ with FastAPI
- **Database**: PostgreSQL for persistent storage
- **Cache**: Redis for session context caching
- **AI Models**: OpenAI GPT-4, Azure OpenAI, DeepSeek (with abstraction for other providers)
- **Authentication**: JWT tokens with OAuth2
- **Deployment**: Docker & Docker Compose

### Project Structure
```
├── app/
│   ├── main.py                 # FastAPI application entry point
│   ├── api/                    # API endpoints
│   │   └── auth.py             # Authentication endpoints
│   ├── core/                   # Core functionality
│   │   ├── config.py           # Configuration management
│   │   ├── security.py         # Security utilities (JWT, password hashing)
│   │   ├── dependencies.py     # FastAPI dependencies
│   │   └── exceptions.py       # Custom exceptions
│   ├── models/                 # SQLAlchemy models
│   │   ├── user.py             # User model
│   │   ├── session.py          # Session model
│   │   └── message.py          # Message model
│   ├── schemas/                # Pydantic schemas
│   │   ├── auth.py             # Authentication schemas
│   │   ├── session.py          # Session schemas
│   │   └── chat.py             # Chat schemas
│   └── agent/                  # AI agent system
│       └── role_loader.py      # Role configuration loader
├── roles/
│   └── default_roles.yaml      # AI role definitions
├── docker-compose.yml          # Docker services configuration
├── Dockerfile                  # Docker image configuration
├── requirements.txt            # Python dependencies
└── .env.example               # Environment variables template
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.11+ (for local development)

### Using Docker (Recommended)

1. **Clone and setup environment**:
   ```bash
   git clone <repository-url>
   cd ai-agent-system
   cp .env.example .env
   # Edit .env with your OpenAI API key and other settings
   ```

2. **Start services**:
   ```bash
   docker-compose up -d
   ```

3. **Access the API**:
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

### Local Development

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Setup environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start PostgreSQL and Redis** (via Docker):
   ```bash
   docker-compose up -d db redis
   ```

4. **Run the application**:
   ```bash
   uvicorn app.main:app --reload
   ```

## 📚 API Usage

### Authentication

**Register a new user**:
```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "securepassword"
  }'
```

**Login**:
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=securepassword"
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 86400
}
```

### Using the API with Authentication

Include the JWT token in the Authorization header:
```bash
curl -X GET "http://localhost:8000/api/v1/sessions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🤖 AI Roles

The system comes with predefined AI roles, each with specialized capabilities:

### Available Roles
- **teacher**: Mathematics educator with step-by-step explanations
- **programmer**: Software development expert with code execution capabilities
- **doctor**: Medical assistant providing health information
- **assistant**: General-purpose AI assistant
- **writer**: Creative and technical writing specialist

### Role Configuration
Roles are defined in `roles/default_roles.yaml`:

```yaml
teacher:
  display_name: "Math Teacher"
  description: "Expert mathematics educator"
  system_prompt: |
    You are an experienced mathematics teacher...
  tools:
    - math_solver
    - knowledge_search
  config:
    model: "gpt-4"
    temperature: 0.7
    max_tokens: 2000
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `REDIS_URL` | Redis connection string | Required |
| `LLM_PROVIDER` | LLM provider to use | `deepseek` |
| `OPENAI_API_KEY` | OpenAI API key | Optional |
| `OPENAI_MODEL` | OpenAI model name | `gpt-4` |
| `AZURE_OPENAI_API_KEY` | Azure OpenAI API key | Optional |
| `AZURE_OPENAI_ENDPOINT` | Azure OpenAI endpoint URL | Optional |
| `AZURE_OPENAI_DEPLOYMENT` | Azure OpenAI deployment name | Optional |
| `AZURE_OPENAI_API_VERSION` | Azure OpenAI API version | `2024-02-15-preview` |
| `DEEPSEEK_API_KEY` | DeepSeek API key | Optional |
| `DEEPSEEK_MODEL` | DeepSeek model name | `deepseek-chat` |
| `SECRET_KEY` | JWT signing secret | Required |
| `MAX_SESSIONS_PER_USER` | Max sessions per user | 10 |
| `JWT_EXPIRE_HOURS` | JWT token expiration | 24 |

### LLM Providers

The system supports multiple LLM providers that can be configured via environment variables:

#### Supported Providers

1. **DeepSeek** (Default)
   - Cost-effective option with good performance
   - Set `LLM_PROVIDER=deepseek`
   - Requires `DEEPSEEK_API_KEY`

2. **OpenAI**
   - Industry-standard GPT models
   - Set `LLM_PROVIDER=openai`
   - Requires `OPENAI_API_KEY`

3. **Azure OpenAI**
   - Enterprise-grade with data residency
   - Set `LLM_PROVIDER=azure_openai`
   - Requires `AZURE_OPENAI_API_KEY`, `AZURE_OPENAI_ENDPOINT`, and `AZURE_OPENAI_DEPLOYMENT`

#### Provider Configuration Examples

```env
# Using DeepSeek (Default)
LLM_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_deepseek_api_key

# Using OpenAI
LLM_PROVIDER=openai
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# Using Azure OpenAI
LLM_PROVIDER=azure_openai
AZURE_OPENAI_API_KEY=your_azure_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=your-deployment-name
```

For detailed Azure OpenAI setup instructions, see [docs/azure_openai_provider.md](docs/azure_openai_provider.md).

### Embedding Support

The system includes comprehensive embedding support for text vectorization, similarity search, and clustering:

```env
# Embedding Provider Configuration
EMBEDDING_PROVIDER=azure_openai

# OpenAI Embeddings
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# Azure OpenAI Embeddings
AZURE_OPENAI_EMBEDDING_DEPLOYMENT=your_embedding_deployment_name
AZURE_OPENAI_EMBEDDING_MODEL=text-embedding-3-small
```

For detailed embedding setup and usage instructions, see [docs/embedding_support.md](docs/embedding_support.md).

### Session Constraints
- **Session names**: 3-100 characters, alphanumeric and spaces only
- **Must be unique per user**
- **Maximum sessions per user**: Configurable (default: 10)

## 🧪 Development Status

### ✅ Completed (Phase 1: Foundation)
- [x] Project structure and configuration
- [x] Database models (User, Session, Message)
- [x] Authentication system with JWT
- [x] User registration and login endpoints
- [x] Role configuration system
- [x] Docker setup
- [x] Pydantic schemas
- [x] Error handling and exceptions
- [x] Core security utilities

### ✅ Completed (Phase 2: Session Management)
- [x] Session management endpoints
- [x] Redis integration for session caching
- [x] Session CRUD operations (Create, Read, Update, Delete)
- [x] Session context storage and retrieval
- [x] Role listing and details endpoints
- [x] Session naming constraints validation
- [x] User session limits enforcement
- [x] Database table auto-creation for development

### ✅ Completed (Phase 3: LLM Integration)
- [x] LLM provider abstraction layer
- [x] OpenAI GPT-4 integration
- [x] Agent factory and memory management
- [x] Role-based AI agent creation
- [x] Conversation context persistence
- [x] Streaming response support
- [x] Chat API endpoints

### ✅ Completed (Phase 4: Real-time Communication)
- [x] WebSocket implementation for real-time chat
- [x] Streaming message protocol
- [x] WebSocket authentication
- [x] Connection management
- [x] Real-time AI response streaming
- [x] Error handling and reconnection support

### ✅ Completed (Phase 5: Tools & Framework)
- [x] Tool framework architecture
- [x] Math solver tool implementation
- [x] General calculator tool
- [x] Tool registry system
- [x] Comprehensive testing suite
- [x] Complete system integration

### ✅ Completed (Phase 6: Embedding Support)
- [x] Embedding provider abstraction layer
- [x] Azure OpenAI embedding provider
- [x] OpenAI embedding provider
- [x] Embedding service layer with similarity operations
- [x] Text clustering and semantic search
- [x] Embedding API endpoints
- [x] Provider factory and configuration management
- [x] Comprehensive embedding documentation

### 🚀 System Ready for Production
All core features implemented and tested!

## 🧪 Testing

A test script is provided to verify API functionality:

```bash
# Start the services
docker-compose up -d

# Wait for services to be ready, then run tests
python test_api.py

# Or test against a different URL
python test_api.py http://localhost:8000
```

The test script will verify:
- Health check endpoint
- User registration and login
- Role listing
- Session creation, listing, updating
- Authentication and authorization

## 🚀 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login (form data)
- `POST /api/v1/auth/login-json` - User login (JSON)

### Session Management
- `GET /api/v1/sessions/` - List user sessions
- `POST /api/v1/sessions/` - Create new session
- `GET /api/v1/sessions/{session_key}` - Get session details
- `PUT /api/v1/sessions/{session_key}` - Update session
- `DELETE /api/v1/sessions/{session_key}` - Delete session
- `GET /api/v1/sessions/roles/` - List available AI roles
- `GET /api/v1/sessions/roles/{role_name}` - Get role details

### Chat API
- `POST /api/v1/chat/` - Send chat message (REST)
- `GET /api/v1/chat/{session_key}/history` - Get message history
- `DELETE /api/v1/chat/{session_key}/history` - Clear message history
- `GET /api/v1/chat/{session_key}/agent-info` - Get agent information

### WebSocket
- `WS /ws/chat?session_key={key}&token={jwt}` - Real-time streaming chat

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For questions or issues, please:
1. Check the API documentation at `/docs`
2. Review the logs for error details
3. Create an issue in the repository

---

**Note**: This is a development version. For production use, ensure proper security configurations, rate limiting, and monitoring are in place.