# Project Overview

## Purpose
This is a **Multi-User, Multi-Session, Multi-Role AI Agent System** built with FastAPI. The system provides:

- **Multi-User Support**: User registration and authentication with JWT tokens
- **Multi-Session Management**: Users can create, name, and manage multiple chat sessions
- **Multi-Role AI System**: Predefined AI roles (Teacher, Programmer, Doctor, etc.) with specialized capabilities
- **Real-time Communication**: WebSocket support for streaming responses
- **Session Persistence**: Redis caching with PostgreSQL storage
- **Role-based Tools**: Each AI role has access to specific tools and capabilities

## Tech Stack
- **Backend**: Python 3.11+ with FastAPI
- **Database**: PostgreSQL for persistent storage, Redis for session caching
- **AI Models**: Supports OpenAI GPT-4 and DeepSeek (configurable)
- **Authentication**: JWT tokens with OAuth2, bcrypt password hashing
- **WebSockets**: Real-time streaming chat support
- **Deployment**: Docker & Docker Compose
- **Testing**: pytest with async support

## Key Features
- Real-time AI chat with streaming responses
- User session management with context persistence
- Role-based AI agents with specialized tools
- Comprehensive authentication and security
- Production-ready with Docker deployment
- Full REST API with WebSocket support