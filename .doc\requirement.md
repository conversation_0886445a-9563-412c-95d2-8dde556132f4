### 项目名称
- 多用户、多 Session、多角色 AI Agent 系统

### 项目目标
- 构建一个基于 Python 的系统，支持多用户同时使用，每个用户可以开启多个会话（Session），每个会话可绑定预定义的AI角色（Role）。
- 支持实时通信（WebSocket）、上下文隔离、数据持久化存储，并使用高级模型如GPT系列或Qwen等。

### 核心功能需求
1. **用户管理**
   - 注册与登录：提供注册接口和JWT认证生成Token。
2. **Session管理**
   - 创建、命名、切换、恢复及删除Session。
3. **角色系统**
   - 预设多种角色（如“老师”、“程序员”），每种角色有特定的提示词、工具权限、知识库来源等配置。
4. **工具调用**
   - 每个角色可启用不同的工具集（如数学计算、代码执行等）。
5. **实时通信**
   - 支持WebSocket，实现即时聊天交互。
6. **数据持久化**
   - 使用Redis缓存每个Session的上下文；PostgreSQL存储用户、Session、消息记录等信息。
7. **安全性**
   - 所有API请求需携带有效的JWT Token进行身份验证。
8. **部署**
   - 使用Docker Compose一键部署数据库、Redis、后端服务。

### 技术栈要求
- 后端语言：Python 3.10+
- Web框架：FastAPI
- 大模型支持：OpenAI GPT-4, Qwen等
- 数据库：PostgreSQL, Redis
- 用户认证：JWT + OAuth2PasswordBearer
- 工具模块：自定义（如math_solver, code_executor）
- 日志：logging / 自定义logger
- 配置管理：Pydantic Settings
- 部署方式：Docker + Docker Compose

### 关键模块说明
- 用户认证模块：包括注册、登录接口，使用JWT进行访问控制。
- Session管理模块：支持创建、恢复、删除会话，并允许为会话命名。
- 角色系统：通过YAML文件定义角色，包含名称、描述、提示词模板等。
- WebSocket实时聊天：支持流式输出，适用于网页端或其他客户端。
- 数据库集成：使用PostgreSQL存储用户、Session、对话记录等，并利用Alembic进行版本管理。
- Redis缓存上下文：用于快速加载和更新每个Session的状态。
- 工具模块：每个角色绑定一组特定工具。
