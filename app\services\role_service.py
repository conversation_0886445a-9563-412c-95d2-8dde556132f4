import yaml
import os
from typing import Dict, <PERSON>, Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
import logging

from app.models.role import Role
from app.schemas.role import RoleCreate, RoleUpdate, RoleSyncResponse
from app.core.config import settings
from app.core.exceptions import ValidationError, NotFoundError, ConflictError

logger = logging.getLogger(__name__)


class RoleService:
    """Service layer for role management operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_role_by_name(self, name: str) -> Optional[Role]:
        """Get a role by name."""
        return self.db.query(Role).filter(Role.name == name).first()
    
    def get_role_by_id(self, role_id: str) -> Optional[Role]:
        """Get a role by ID."""
        return self.db.query(Role).filter(Role.id == role_id).first()
    
    def list_roles(self, active_only: bool = True) -> List[Role]:
        """List all roles, optionally filtering by active status."""
        query = self.db.query(Role)
        if active_only:
            query = query.filter(Role.is_active == True)
        return query.order_by(Role.name).all()
    
    def create_role(self, role_data: RoleCreate) -> Role:
        """Create a new role."""
        # Check if role with same name already exists
        existing_role = self.get_role_by_name(role_data.name)
        if existing_role:
            raise ConflictError(f"Role with name '{role_data.name}' already exists")
        
        try:
            db_role = Role(
                name=role_data.name,
                display_name=role_data.display_name,
                description=role_data.description,
                system_prompt=role_data.system_prompt,
                tools=role_data.tools,
                config=role_data.config,
                is_active=role_data.is_active
            )
            self.db.add(db_role)
            self.db.commit()
            self.db.refresh(db_role)
            logger.info(f"Created role: {role_data.name}")
            return db_role
        except IntegrityError as e:
            self.db.rollback()
            raise ConflictError(f"Role creation failed: {str(e)}")
    
    def update_role(self, name: str, role_data: RoleUpdate) -> Optional[Role]:
        """Update an existing role."""
        role = self.get_role_by_name(name)
        if not role:
            raise NotFoundError(f"Role '{name}' not found")
        
        try:
            # Update only provided fields
            update_data = role_data.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(role, key, value)
            
            self.db.commit()
            self.db.refresh(role)
            logger.info(f"Updated role: {name}")
            return role
        except IntegrityError as e:
            self.db.rollback()
            raise ConflictError(f"Role update failed: {str(e)}")
    
    def delete_role(self, name: str) -> bool:
        """Delete a role (soft delete by setting is_active=False)."""
        role = self.get_role_by_name(name)
        if not role:
            raise NotFoundError(f"Role '{name}' not found")
        
        role.is_active = False
        self.db.commit()
        logger.info(f"Deactivated role: {name}")
        return True
    
    def hard_delete_role(self, name: str) -> bool:
        """Permanently delete a role from database."""
        role = self.get_role_by_name(name)
        if not role:
            raise NotFoundError(f"Role '{name}' not found")
        
        self.db.delete(role)
        self.db.commit()
        logger.info(f"Permanently deleted role: {name}")
        return True
    
    def load_yaml_roles(self, config_path: str = None) -> Dict[str, dict]:
        """Load roles from YAML configuration file."""
        config_path = config_path or settings.roles_config_path
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Roles configuration file not found: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                roles_data = yaml.safe_load(file)
            
            if not roles_data:
                raise ValidationError("No roles found in configuration file")
            
            logger.info(f"Loaded {len(roles_data)} roles from YAML: {config_path}")
            return roles_data
        except yaml.YAMLError as e:
            raise ValidationError(f"Invalid YAML format: {str(e)}")
        except Exception as e:
            raise ValidationError(f"Error loading YAML file: {str(e)}")
    
    def sync_yaml_to_database(self, config_path: str = None, overwrite_existing: bool = False) -> RoleSyncResponse:
        """Sync roles from YAML file to database."""
        yaml_roles = self.load_yaml_roles(config_path)
        
        created_count = 0
        updated_count = 0
        synced_roles = []
        
        for role_name, role_data in yaml_roles.items():
            try:
                existing_role = self.get_role_by_name(role_name)
                
                if existing_role:
                    if overwrite_existing:
                        # Update existing role
                        role_update = RoleUpdate(
                            display_name=role_data.get('display_name'),
                            description=role_data.get('description'),
                            system_prompt=role_data.get('system_prompt'),
                            tools=role_data.get('tools', []),
                            config=role_data.get('config', {}),
                            is_active=role_data.get('is_active', True)
                        )
                        self.update_role(role_name, role_update)
                        updated_count += 1
                        synced_roles.append(role_name)
                        logger.info(f"Updated role from YAML: {role_name}")
                    else:
                        logger.info(f"Skipped existing role: {role_name}")
                else:
                    # Create new role
                    role_create = RoleCreate(
                        name=role_name,
                        display_name=role_data.get('display_name', role_name),
                        description=role_data.get('description'),
                        system_prompt=role_data.get('system_prompt', ''),
                        tools=role_data.get('tools', []),
                        config=role_data.get('config', {}),
                        is_active=role_data.get('is_active', True)
                    )
                    self.create_role(role_create)
                    created_count += 1
                    synced_roles.append(role_name)
                    logger.info(f"Created role from YAML: {role_name}")
                    
            except Exception as e:
                logger.error(f"Error syncing role '{role_name}': {str(e)}")
                continue
        
        message = f"Sync completed: {created_count} created, {updated_count} updated"
        logger.info(message)
        
        return RoleSyncResponse(
            synced_roles=synced_roles,
            created_count=created_count,
            updated_count=updated_count,
            message=message
        )
