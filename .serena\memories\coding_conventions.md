# Coding Conventions

## Python Style
- **Type Hints**: Comprehensive type annotations using `typing` module
- **Pydantic Models**: Used for configuration (`BaseSettings`) and API schemas
- **SQLAlchemy Models**: ORM models inherit from `Base` class
- **Docstrings**: Triple-quoted docstrings for classes and functions
- **Imports**: Absolute imports, grouped (standard library, third-party, local)

## Code Organization
- **Snake_case**: Functions, variables, file names
- **PascalCase**: Classes and Pydantic models
- **UPPER_CASE**: Constants and environment variables
- **Private methods**: Leading underscore `_method_name`

## FastAPI Patterns
- **Router organization**: Separate routers for different API modules
- **Dependency injection**: Use FastAPI `Depends()` for database, auth
- **Exception handling**: Custom exception classes with proper HTTP status codes
- **Response models**: Pydantic schemas for all API responses
- **Async/await**: Async functions for database and external API calls

## Database Patterns
- **UUID primary keys**: Using `UUID(as_uuid=True)` for all models
- **Timestamps**: `created_at`, `updated_at` with automatic updates
- **Relationships**: SQLAlchemy relationships with `back_populates`
- **Indexes**: Strategic indexing on frequently queried fields

## Error Handling
- **Custom exceptions**: Specific exception classes in `core/exceptions.py`
- **HTTP status codes**: Proper status codes for different error types
- **Error responses**: Consistent JSON error format with `detail` and `type`

## Configuration
- **Environment-based**: Pydantic Settings with `.env` file support
- **Validation**: Pydantic field validation with proper defaults
- **Caching**: `@lru_cache()` for settings to load once

## Security
- **Password hashing**: bcrypt via passlib
- **JWT tokens**: python-jose for token handling
- **Environment secrets**: Never hardcode secrets, use environment variables

## Testing
- **pytest**: Async testing with `pytest-asyncio`
- **HTTP testing**: Using `httpx` for async HTTP client testing
- **Test organization**: Separate test files for different modules