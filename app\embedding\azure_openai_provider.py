from typing import Dict, Any, Optional, List, Union
from openai import AsyncAzureOpenAI

from app.embedding.base import (
    EmbeddingProvider,
    EmbeddingRequest,
    EmbeddingResponse,
    EmbeddingData,
    EmbeddingUsage,
    EmbeddingProviderFactory
)
from app.core.config import settings
from app.core.exceptions import LLMError


class AzureOpenAIEmbeddingProvider(EmbeddingProvider):
    """Azure OpenAI embedding provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # Initialize Azure OpenAI client
        api_key = config.get("api_key") or settings.azure_openai_api_key
        if not api_key:
            raise LLMError("Azure OpenAI API key is required for embeddings")
        
        endpoint = config.get("endpoint") or settings.azure_openai_endpoint
        if not endpoint:
            raise LLMError("Azure OpenAI endpoint is required for embeddings")
        
        api_version = config.get("api_version") or settings.azure_openai_api_version
        deployment = config.get("deployment") or settings.azure_openai_embedding_deployment
        if not deployment:
            raise LLMError("Azure OpenAI embedding deployment name is required")
        
        self.client = AsyncAzureOpenAI(
            api_key=api_key,
            azure_endpoint=endpoint,
            api_version=api_version
        )
        self.deployment = deployment
        self.model = config.get("model", settings.azure_openai_embedding_model)
    
    async def create_embeddings(
        self,
        request: EmbeddingRequest,
        **kwargs
    ) -> EmbeddingResponse:
        """
        Create embeddings using Azure OpenAI.
        
        Args:
            request: Embedding request containing text and parameters
            **kwargs: Additional parameters
            
        Returns:
            EmbeddingResponse: Response containing embeddings and metadata
            
        Raises:
            LLMError: If the API call fails
        """
        try:
            # Prepare input text
            input_text = request.text
            if isinstance(input_text, str):
                input_text = [input_text]
            
            # Prepare parameters
            params = {
                "model": self.deployment,  # Use deployment name for Azure
                "input": input_text,
                "encoding_format": request.encoding_format
            }
            
            # Add optional parameters
            if request.dimensions:
                params["dimensions"] = request.dimensions
            if request.user:
                params["user"] = request.user
            
            # Override with any provided kwargs
            params.update(kwargs)
            
            # Make API call
            response = await self.client.embeddings.create(**params)
            
            # Convert to our response format
            embedding_data = []
            for i, item in enumerate(response.data):
                embedding_data.append(EmbeddingData(
                    embedding=item.embedding,
                    index=i
                ))
            
            usage = EmbeddingUsage(
                prompt_tokens=response.usage.prompt_tokens,
                total_tokens=response.usage.total_tokens
            )
            
            return EmbeddingResponse(
                data=embedding_data,
                model=response.model,
                usage=usage
            )
            
        except Exception as e:
            if "azure" in str(type(e)).lower() or "openai" in str(type(e)).lower():
                raise LLMError(f"Azure OpenAI embedding API error: {str(e)}")
            raise LLMError(f"Unexpected error in Azure OpenAI embeddings: {str(e)}")
    
    async def validate_connection(self) -> bool:
        """
        Validate Azure OpenAI embedding connection by making a simple API call.
        
        Returns:
            bool: True if connection is valid
        """
        try:
            # Make a minimal API call to test connection
            test_request = EmbeddingRequest(text="test")
            await self.create_embeddings(test_request)
            return True
            
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current embedding model."""
        return {
            "provider": self.__class__.__name__,
            "model": self.model,
            "deployment": self.deployment,
            "config": {k: v for k, v in self.config.items() if k not in ["api_key"]}
        }


# Register the Azure OpenAI embedding provider
EmbeddingProviderFactory.register_provider("azure_openai", AzureOpenAIEmbeddingProvider)


def create_azure_openai_embedding_provider(config: Optional[Dict[str, Any]] = None) -> AzureOpenAIEmbeddingProvider:
    """
    Convenience function to create an Azure OpenAI embedding provider.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        AzureOpenAIEmbeddingProvider: Configured Azure OpenAI embedding provider instance
    """
    if config is None:
        config = {
            "deployment": settings.azure_openai_embedding_deployment,
            "api_key": settings.azure_openai_api_key,
            "endpoint": settings.azure_openai_endpoint,
            "api_version": settings.azure_openai_api_version,
            "model": settings.azure_openai_embedding_model
        }
    
    return AzureOpenAIEmbeddingProvider(config)
